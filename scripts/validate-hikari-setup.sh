#!/bin/bash

# HikariCP Setup Validation Script
# This script validates the HikariCP implementation and configuration

set -e

echo "=== HikariCP Setup Validation ==="
echo "Starting validation process..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}[PASS]${NC} $message"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}[FAIL]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}[INFO]${NC} $message"
    fi
}

# Variables for tracking
PASS_COUNT=0
FAIL_COUNT=0
WARN_COUNT=0

# Function to increment counters
increment_counter() {
    case $1 in
        "PASS") ((PASS_COUNT++)) ;;
        "FAIL") ((FAIL_COUNT++)) ;;
        "WARN") ((WARN_COUNT++)) ;;
    esac
}

echo ""
echo "1. Checking Maven Dependencies..."

# Check if HikariCP is in dependencies
if grep -q "com.zaxxer" pom.xml && grep -q "HikariCP" pom.xml; then
    print_status "PASS" "HikariCP dependency found in pom.xml"
    increment_counter "PASS"
else
    print_status "FAIL" "HikariCP dependency not found in pom.xml"
    increment_counter "FAIL"
fi

# Check if metrics dependencies are present
if grep -q "metrics-core" pom.xml; then
    print_status "PASS" "Dropwizard metrics-core dependency found"
    increment_counter "PASS"
else
    print_status "FAIL" "Dropwizard metrics-core dependency not found"
    increment_counter "FAIL"
fi

if grep -q "metrics-jmx" pom.xml; then
    print_status "PASS" "Dropwizard metrics-jmx dependency found"
    increment_counter "PASS"
else
    print_status "FAIL" "Dropwizard metrics-jmx dependency not found"
    increment_counter "FAIL"
fi

echo ""
echo "2. Checking Core Implementation Files..."

# Check core implementation files
FILES=(
    "src/main/java/com/nymbl/database/ConnectionPoolFactory.java"
    "src/main/java/com/nymbl/database/CompatibleDataSource.java"
    "src/main/java/com/nymbl/database/BatchDataSourceProvider.java"
    "src/main/java/com/nymbl/database/TenantLifecycleManager.java"
    "src/main/java/com/nymbl/database/DatabaseMonitoringService.java"
    "src/main/java/com/nymbl/config/MetricsConfig.java"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "PASS" "Found required file: $file"
        increment_counter "PASS"
    else
        print_status "FAIL" "Missing required file: $file"
        increment_counter "FAIL"
    fi
done

echo ""
echo "3. Checking Configuration Files..."

CONFIG_FILES=(
    "src/main/resources/application-hikari.properties"
    "src/main/resources/application-production.properties"
    "src/main/resources/application.properties"
)

for file in "${CONFIG_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "PASS" "Found configuration file: $file"
        increment_counter "PASS"
    else
        print_status "WARN" "Missing configuration file: $file"
        increment_counter "WARN"
    fi
done

echo ""
echo "4. Checking Configuration Properties..."

# Check if feature flag is present in main application.properties
if [ -f "src/main/resources/application.properties" ]; then
    if grep -q "nymbl.database.hikari.enabled" src/main/resources/application.properties; then
        print_status "PASS" "HikariCP feature flag found in application.properties"
        increment_counter "PASS"
    else
        print_status "FAIL" "HikariCP feature flag not found in application.properties"
        increment_counter "FAIL"
    fi
fi

echo ""
echo "5. Checking Test Files..."

TEST_FILES=(
    "src/test/java/com/nymbl/database/HikariIntegrationTest.java"
)

for file in "${TEST_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "PASS" "Found test file: $file"
        increment_counter "PASS"
    else
        print_status "WARN" "Missing test file: $file"
        increment_counter "WARN"
    fi
done

echo ""
echo "6. Compilation Check..."

# Try to compile the project
if mvn compile -q > /dev/null 2>&1; then
    print_status "PASS" "Project compiles successfully"
    increment_counter "PASS"
else
    print_status "FAIL" "Project compilation failed"
    increment_counter "FAIL"
    echo "Run 'mvn compile' to see detailed compilation errors"
fi

echo ""
echo "7. Checking Implementation Completeness..."

# Check if critical methods are implemented
IMPLEMENTATIONS=(
    "ConnectionPoolFactory.createDataSource"
    "CompatibleDataSource.getPoolStats"
    "TenantLifecycleManager.createTenant"
    "DatabaseMonitoringService.isHealthy"
)

for impl in "${IMPLEMENTATIONS[@]}"; do
    class=$(echo $impl | cut -d'.' -f1)
    method=$(echo $impl | cut -d'.' -f2)
    file="src/main/java/com/nymbl/database/${class}.java"
    
    if [ -f "$file" ] && grep -q "$method" "$file"; then
        print_status "PASS" "Method $method found in $class"
        increment_counter "PASS"
    else
        print_status "FAIL" "Method $method not found in $class"
        increment_counter "FAIL"
    fi
done

echo ""
echo "8. Feature Flag Validation..."

# Test feature flag toggle
if [ -f "src/main/resources/application.properties" ]; then
    # Check default value
    CURRENT_VALUE=$(grep "nymbl.database.hikari.enabled" src/main/resources/application.properties | cut -d'=' -f2)
    if [ "$CURRENT_VALUE" = "false" ]; then
        print_status "PASS" "Feature flag defaults to false (safe for production)"
        increment_counter "PASS"
    else
        print_status "WARN" "Feature flag defaults to true (consider defaulting to false for safety)"
        increment_counter "WARN"
    fi
fi

echo ""
echo "9. Thread Safety Validation..."

# Check if ConcurrentHashMap is used instead of HashMap
if [ -f "src/main/java/com/nymbl/tenant/MultiTenantConnectionProviderImpl.java" ]; then
    if grep -q "ConcurrentHashMap" src/main/java/com/nymbl/tenant/MultiTenantConnectionProviderImpl.java; then
        print_status "PASS" "Thread-safe ConcurrentHashMap found in MultiTenantConnectionProviderImpl"
        increment_counter "PASS"
    else
        print_status "FAIL" "Thread-safe ConcurrentHashMap not found in MultiTenantConnectionProviderImpl"
        increment_counter "FAIL"
    fi
fi

echo ""
echo "10. Documentation Check..."

DOCS=(
    ".cursor/rules/hikari.md"
    "scripts/rollback-hikari.sh"
)

for doc in "${DOCS[@]}"; do
    if [ -f "$doc" ]; then
        print_status "PASS" "Found documentation: $doc"
        increment_counter "PASS"
    else
        print_status "WARN" "Missing documentation: $doc"
        increment_counter "WARN"
    fi
done

echo ""
echo "=== VALIDATION SUMMARY ==="
echo -e "${GREEN}Passed: $PASS_COUNT${NC}"
echo -e "${RED}Failed: $FAIL_COUNT${NC}"
echo -e "${YELLOW}Warnings: $WARN_COUNT${NC}"

TOTAL=$((PASS_COUNT + FAIL_COUNT + WARN_COUNT))
if [ $TOTAL -gt 0 ]; then
    PASS_PERCENTAGE=$((PASS_COUNT * 100 / TOTAL))
    echo "Success Rate: ${PASS_PERCENTAGE}%"
fi

echo ""
if [ $FAIL_COUNT -eq 0 ]; then
    print_status "PASS" "All critical validations passed! HikariCP implementation is ready."
    echo ""
    echo "Next Steps:"
    echo "1. Test in development environment first"
    echo "2. Set nymbl.database.hikari.enabled=true to enable HikariCP"
    echo "3. Monitor application logs and JMX metrics"
    echo "4. Gradually roll out to production environments"
    exit 0
else
    print_status "FAIL" "Some validations failed. Please address the issues before proceeding."
    echo ""
    echo "Recommended Actions:"
    echo "1. Fix compilation errors if any"
    echo "2. Ensure all required files are present"
    echo "3. Run tests to verify functionality"
    echo "4. Review configuration files"
    exit 1
fi 