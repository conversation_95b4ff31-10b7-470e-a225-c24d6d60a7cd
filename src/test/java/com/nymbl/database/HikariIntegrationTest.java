package com.nymbl.database;

import com.codahale.metrics.MetricRegistry;
import com.nymbl.master.model.Company;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for HikariCP functionality.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(properties = {
    "nymbl.database.hikari.enabled=true",
    "nymbl.database.pool.type=hikari"
})
class HikariIntegrationTest {

    @Autowired
    private ConnectionPoolFactory connectionPoolFactory;

    @Autowired
    private MetricRegistry metricRegistry;

    private ConnectionPoolFactory.DatabaseConfig testConfig;

    @BeforeEach
    void setUp() {
        testConfig = new ConnectionPoolFactory.DatabaseConfig("test_db");
        testConfig.setJdbcUrl("jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE");
        testConfig.setUsername("sa");
        testConfig.setPassword("");
        testConfig.setDriverClassName("org.h2.Driver");
        testConfig.setMaxPoolSize(10);
        testConfig.setMinPoolSize(2);
    }

    @Test
    void testHikariEnabled() {
        assertTrue(connectionPoolFactory.isHikariEnabled(), 
                   "HikariCP should be enabled in test configuration");
    }

    @Test
    void testCreateHikariDataSource() {
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        
        assertNotNull(dataSource, "DataSource should not be null");
        assertTrue(dataSource instanceof com.zaxxer.hikari.HikariDataSource, 
                   "DataSource should be HikariDataSource instance");
    }

    @Test
    void testHikariConnectionRetrieval() throws SQLException {
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertFalse(connection.isClosed(), "Connection should be open");
            assertTrue(connection.isValid(5), "Connection should be valid");
        }
    }

    @Test
    void testCompatibleDataSourceWrapper() {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");
        
        assertNotNull(compatibleDataSource, "CompatibleDataSource should not be null");
        assertEquals("test_db", compatibleDataSource.getDatabaseName());
        assertEquals("hikari", compatibleDataSource.getPoolType());
        assertFalse(compatibleDataSource.isClosed());
    }

    @Test
    void testPoolStatistics() {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");
        
        CompatibleDataSource.PoolStats stats = compatibleDataSource.getPoolStats();
        assertNotNull(stats, "Pool stats should not be null");
        assertEquals("hikari", stats.getPoolType());
        assertTrue(stats.getTotalConnections() >= 0, "Total connections should be non-negative");
    }

    @Test
    void testConnectionPoolClosing() {
        DataSource rawDataSource = connectionPoolFactory.createDataSource(testConfig);
        CompatibleDataSource compatibleDataSource = new CompatibleDataSource(
            rawDataSource, "test_db", "hikari");
        
        assertFalse(compatibleDataSource.isClosed(), "DataSource should not be closed initially");
        
        compatibleDataSource.close();
        assertTrue(compatibleDataSource.isClosed(), "DataSource should be closed after close()");
        
        // Test that getting connection after close throws exception
        assertThrows(SQLException.class, compatibleDataSource::getConnection,
                     "Should throw SQLException when getting connection from closed DataSource");
    }

    @Test
    void testMultipleConnections() throws SQLException {
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        
        // Test multiple concurrent connections
        try (Connection conn1 = dataSource.getConnection();
             Connection conn2 = dataSource.getConnection()) {
            
            assertNotNull(conn1, "First connection should not be null");
            assertNotNull(conn2, "Second connection should not be null");
            assertNotSame(conn1, conn2, "Connections should be different instances");
        }
    }

    @Test
    void testTenantLifecycleManager() {
        // This test would require mocking the CompanyRepository and other dependencies
        // For now, just verify the class can be instantiated
        assertNotNull(connectionPoolFactory, "ConnectionPoolFactory should be available");
    }

    @Test
    void testMetricsIntegration() {
        assertNotNull(metricRegistry, "MetricRegistry should be available");
        
        DataSource dataSource = connectionPoolFactory.createDataSource(testConfig);
        
        // HikariCP should register metrics
        assertFalse(metricRegistry.getMetrics().isEmpty(), 
                   "Metrics should be registered when HikariCP is used");
    }
} 