package com.nymbl.database;

import com.codahale.metrics.MetricRegistry;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.tomcat.jdbc.pool.PoolProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * Factory for creating connection pools with support for both HikariCP and Tomcat JDBC.
 * Allows gradual migration through feature flags.
 */
@Component
public class ConnectionPoolFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolFactory.class);
    
    @Value("${nymbl.database.pool.type:tomcat}")
    private String poolType;
    
    @Value("${nymbl.database.hikari.enabled:false}")
    private boolean hikariEnabled;
    
    /**
     * Check if Hikari<PERSON> is enabled.
     */
    public boolean isHikariEnabled() {
        return hikariEnabled;
    }
    

    
    private final MetricRegistry metricRegistry;
    
    public ConnectionPoolFactory(MetricRegistry metricRegistry) {
        this.metricRegistry = metricRegistry;
    }
    
    /**
     * Creates a DataSource based on the configured pool type.
     */
    public DataSource createDataSource(DatabaseConfig config) {
        if (hikariEnabled || "hikari".equalsIgnoreCase(poolType)) {
            return createHikariDataSource(config);
        } else {
            return createTomcatDataSource(config);
        }
    }
    
    /**
     * Creates a HikariCP DataSource with monitoring.
     */
    private DataSource createHikariDataSource(DatabaseConfig config) {
        logger.info("Creating HikariCP DataSource for database: {}", config.getDatabaseName());
        
        HikariConfig hikariConfig = new HikariConfig();
        
        // Basic connection properties
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(config.getDriverClassName());
        
        // Pool sizing
        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        hikariConfig.setMinimumIdle(config.getMinPoolSize());
        
        // Connection validation
        hikariConfig.setConnectionTestQuery(config.getValidationQuery());
        hikariConfig.setValidationTimeout(config.getValidationTimeout());
        
        // Timeouts
        hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        hikariConfig.setIdleTimeout(config.getIdleTimeout());
        hikariConfig.setMaxLifetime(config.getMaxLifetime());
        
        // Pool name for monitoring
        hikariConfig.setPoolName(config.getPoolName());
        
        // Metrics
        if (metricRegistry != null) {
            hikariConfig.setMetricRegistry(metricRegistry);
        }
        
        // Performance optimizations
        hikariConfig.setLeakDetectionThreshold(config.getLeakDetectionThreshold());
        
        // Statement caching via JDBC properties
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        
        try {
            HikariDataSource dataSource = new HikariDataSource(hikariConfig);
            logger.info("Successfully created HikariCP DataSource for: {}", config.getDatabaseName());
            return dataSource;
        } catch (Exception e) {
            logger.error("Failed to create HikariCP DataSource for: {}", config.getDatabaseName(), e);
            // Fallback to Tomcat JDBC
            logger.warn("Falling back to Tomcat JDBC for: {}", config.getDatabaseName());
            return createTomcatDataSource(config);
        }
    }
    
    /**
     * Creates a Tomcat JDBC DataSource (existing implementation).
     */
    private DataSource createTomcatDataSource(DatabaseConfig config) {
        logger.info("Creating Tomcat JDBC DataSource for database: {}", config.getDatabaseName());
        
        PoolProperties poolProperties = new PoolProperties();
        
        // Basic connection properties
        poolProperties.setUrl(config.getJdbcUrl());
        poolProperties.setUsername(config.getUsername());
        poolProperties.setPassword(config.getPassword());
        poolProperties.setDriverClassName(config.getDriverClassName());
        
        // Pool sizing
        poolProperties.setMaxActive(config.getMaxPoolSize());
        poolProperties.setMinIdle(config.getMinPoolSize());
        poolProperties.setInitialSize(config.getMinPoolSize());
        
        // Connection validation
        poolProperties.setValidationQuery(config.getValidationQuery());
        poolProperties.setValidationQueryTimeout(config.getValidationTimeout() / 1000); // Convert to seconds
        poolProperties.setTestOnBorrow(true);
        poolProperties.setTestOnReturn(false);
        poolProperties.setTestWhileIdle(true);
        
        // Timeouts (convert from milliseconds to seconds for Tomcat)
        poolProperties.setMaxWait((int) config.getConnectionTimeout());
        poolProperties.setMinEvictableIdleTimeMillis((int) config.getIdleTimeout());
        poolProperties.setMaxAge((int) config.getMaxLifetime());
        
        // Pool maintenance
        poolProperties.setTimeBetweenEvictionRunsMillis(30000);
        poolProperties.setNumTestsPerEvictionRun(3);
        
        // Leak detection
        poolProperties.setSuspectTimeout((int) (config.getLeakDetectionThreshold() / 1000)); // Convert to seconds
        poolProperties.setLogAbandoned(true);
        poolProperties.setRemoveAbandoned(true);
        poolProperties.setRemoveAbandonedTimeout(300);
        
        org.apache.tomcat.jdbc.pool.DataSource dataSource = new org.apache.tomcat.jdbc.pool.DataSource(poolProperties);
        logger.info("Successfully created Tomcat JDBC DataSource for: {}", config.getDatabaseName());
        return dataSource;
    }
    
    /**
     * Configuration holder for database connection parameters.
     */
    public static class DatabaseConfig {
        private String databaseName;
        private String jdbcUrl;
        private String username;
        private String password;
        private String driverClassName;
        private int maxPoolSize;
        private int minPoolSize;
        private String validationQuery;
        private int validationTimeout;
        private long connectionTimeout;
        private long idleTimeout;
        private long maxLifetime;
        private String poolName;
        private long leakDetectionThreshold;
        
        // Constructor
        public DatabaseConfig(String databaseName) {
            this.databaseName = databaseName;
            // Set defaults
            this.maxPoolSize = 20;
            this.minPoolSize = 5;
            this.validationQuery = "SELECT 1";
            this.validationTimeout = 5000;
            this.connectionTimeout = 30000;
            this.idleTimeout = 600000;
            this.maxLifetime = 1800000;
            this.leakDetectionThreshold = 60000;
            this.poolName = databaseName + "-pool";
        }
        
        // Getters and setters
        public String getDatabaseName() { return databaseName; }
        public void setDatabaseName(String databaseName) { this.databaseName = databaseName; }
        
        public String getJdbcUrl() { return jdbcUrl; }
        public void setJdbcUrl(String jdbcUrl) { this.jdbcUrl = jdbcUrl; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public String getDriverClassName() { return driverClassName; }
        public void setDriverClassName(String driverClassName) { this.driverClassName = driverClassName; }
        
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        
        public int getMinPoolSize() { return minPoolSize; }
        public void setMinPoolSize(int minPoolSize) { this.minPoolSize = minPoolSize; }
        
        public String getValidationQuery() { return validationQuery; }
        public void setValidationQuery(String validationQuery) { this.validationQuery = validationQuery; }
        
        public int getValidationTimeout() { return validationTimeout; }
        public void setValidationTimeout(int validationTimeout) { this.validationTimeout = validationTimeout; }
        
        public long getConnectionTimeout() { return connectionTimeout; }
        public void setConnectionTimeout(long connectionTimeout) { this.connectionTimeout = connectionTimeout; }
        
        public long getIdleTimeout() { return idleTimeout; }
        public void setIdleTimeout(long idleTimeout) { this.idleTimeout = idleTimeout; }
        
        public long getMaxLifetime() { return maxLifetime; }
        public void setMaxLifetime(long maxLifetime) { this.maxLifetime = maxLifetime; }
        
        public String getPoolName() { return poolName; }
        public void setPoolName(String poolName) { this.poolName = poolName; }
        
        public long getLeakDetectionThreshold() { return leakDetectionThreshold; }
        public void setLeakDetectionThreshold(long leakDetectionThreshold) { this.leakDetectionThreshold = leakDetectionThreshold; }
    }
} 