package com.nymbl.database;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Wrapper DataSource that provides a compatible interface for both HikariCP and Tomcat JDBC.
 * Adds lifecycle management and monitoring capabilities.
 */
public class CompatibleDataSource implements DataSource {
    
    private static final Logger logger = LoggerFactory.getLogger(CompatibleDataSource.class);
    
    private final DataSource delegate;
    private final String databaseName;
    private final String poolType;
    private final AtomicBoolean closed = new AtomicBoolean(false);
    
    public CompatibleDataSource(DataSource delegate, String databaseName, String poolType) {
        this.delegate = delegate;
        this.databaseName = databaseName;
        this.poolType = poolType;
        logger.info("Created CompatibleDataSource for database: {} using pool type: {}", databaseName, poolType);
    }
    
    @Override
    public Connection getConnection() throws SQLException {
        if (closed.get()) {
            throw new SQLException("DataSource has been closed for database: " + databaseName);
        }
        
        try {
            Connection connection = delegate.getConnection();
            if (logger.isDebugEnabled()) {
                logger.debug("Retrieved connection for database: {} using pool: {}", databaseName, poolType);
            }
            return connection;
        } catch (SQLException e) {
            logger.error("Failed to get connection for database: {} using pool: {}", databaseName, poolType, e);
            throw e;
        }
    }
    
    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        if (closed.get()) {
            throw new SQLException("DataSource has been closed for database: " + databaseName);
        }
        
        try {
            Connection connection = delegate.getConnection(username, password);
            if (logger.isDebugEnabled()) {
                logger.debug("Retrieved connection with credentials for database: {} using pool: {}", databaseName, poolType);
            }
            return connection;
        } catch (SQLException e) {
            logger.error("Failed to get connection with credentials for database: {} using pool: {}", databaseName, poolType, e);
            throw e;
        }
    }
    
    @Override
    public PrintWriter getLogWriter() throws SQLException {
        return delegate.getLogWriter();
    }
    
    @Override
    public void setLogWriter(PrintWriter out) throws SQLException {
        delegate.setLogWriter(out);
    }
    
    @Override
    public void setLoginTimeout(int seconds) throws SQLException {
        delegate.setLoginTimeout(seconds);
    }
    
    @Override
    public int getLoginTimeout() throws SQLException {
        return delegate.getLoginTimeout();
    }
    
    @Override
    public java.util.logging.Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return delegate.getParentLogger();
    }
    
    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        // First try to unwrap the delegate
        if (delegate.isWrapperFor(iface)) {
            return delegate.unwrap(iface);
        }
        
        // Then try this instance
        if (iface.isAssignableFrom(getClass())) {
            return iface.cast(this);
        }
        
        throw new SQLException("Cannot unwrap to " + iface.getName());
    }
    
    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isAssignableFrom(getClass()) || delegate.isWrapperFor(iface);
    }
    
    /**
     * Close the underlying DataSource if it supports closing.
     */
    public void close() {
        if (closed.compareAndSet(false, true)) {
            logger.info("Closing DataSource for database: {} using pool: {}", databaseName, poolType);
            
            try {
                // Try to close HikariDataSource
                if (delegate instanceof com.zaxxer.hikari.HikariDataSource) {
                    ((com.zaxxer.hikari.HikariDataSource) delegate).close();
                    logger.info("Successfully closed HikariCP DataSource for: {}", databaseName);
                }
                // Try to close Tomcat DataSource
                else if (delegate instanceof org.apache.tomcat.jdbc.pool.DataSource) {
                    ((org.apache.tomcat.jdbc.pool.DataSource) delegate).close();
                    logger.info("Successfully closed Tomcat JDBC DataSource for: {}", databaseName);
                }
                else {
                    logger.warn("Unknown DataSource type for database: {}, cannot close gracefully", databaseName);
                }
            } catch (Exception e) {
                logger.error("Error closing DataSource for database: {}", databaseName, e);
            }
        }
    }
    
    /**
     * Get pool statistics if available.
     */
    public PoolStats getPoolStats() {
        try {
            if (delegate instanceof com.zaxxer.hikari.HikariDataSource) {
                com.zaxxer.hikari.HikariDataSource hikariDS = (com.zaxxer.hikari.HikariDataSource) delegate;
                com.zaxxer.hikari.HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                
                return new PoolStats(
                    poolBean.getActiveConnections(),
                    poolBean.getIdleConnections(),
                    poolBean.getTotalConnections(),
                    poolBean.getThreadsAwaitingConnection(),
                    poolType
                );
            }
            else if (delegate instanceof org.apache.tomcat.jdbc.pool.DataSource) {
                org.apache.tomcat.jdbc.pool.DataSource tomcatDS = (org.apache.tomcat.jdbc.pool.DataSource) delegate;
                org.apache.tomcat.jdbc.pool.ConnectionPool pool = tomcatDS.getPool();
                
                return new PoolStats(
                    pool.getActive(),
                    pool.getIdle(),
                    pool.getSize(),
                    pool.getWaitCount(),
                    poolType
                );
            }
        } catch (Exception e) {
            logger.debug("Could not retrieve pool stats for database: {}", databaseName, e);
        }
        
        return new PoolStats(0, 0, 0, 0, poolType);
    }
    
    /**
     * Check if the DataSource is closed.
     */
    public boolean isClosed() {
        return closed.get();
    }
    
    /**
     * Get the database name this DataSource serves.
     */
    public String getDatabaseName() {
        return databaseName;
    }
    
    /**
     * Get the pool type (hikari or tomcat).
     */
    public String getPoolType() {
        return poolType;
    }
    
    /**
     * Get the underlying DataSource.
     */
    public DataSource getDelegate() {
        return delegate;
    }
    
    /**
     * Pool statistics holder.
     */
    public static class PoolStats {
        private final int activeConnections;
        private final int idleConnections;
        private final int totalConnections;
        private final int waitingThreads;
        private final String poolType;
        
        public PoolStats(int activeConnections, int idleConnections, int totalConnections, int waitingThreads, String poolType) {
            this.activeConnections = activeConnections;
            this.idleConnections = idleConnections;
            this.totalConnections = totalConnections;
            this.waitingThreads = waitingThreads;
            this.poolType = poolType;
        }
        
        public int getActiveConnections() { return activeConnections; }
        public int getIdleConnections() { return idleConnections; }
        public int getTotalConnections() { return totalConnections; }
        public int getWaitingThreads() { return waitingThreads; }
        public String getPoolType() { return poolType; }
        
        @Override
        public String toString() {
            return String.format("PoolStats{type=%s, active=%d, idle=%d, total=%d, waiting=%d}", 
                poolType, activeConnections, idleConnections, totalConnections, waitingThreads);
        }
    }
} 