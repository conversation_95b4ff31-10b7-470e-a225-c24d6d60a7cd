package com.nymbl.tenant;

import com.nymbl.config.TenantDatabaseConfig;
import com.nymbl.database.CompatibleDataSource;
import com.nymbl.database.ConnectionPoolFactory;
import com.nymbl.master.model.Company;
import com.nymbl.master.repository.CompanyRepository;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.jdbc.connections.spi.AbstractDataSourceBasedMultiTenantConnectionProviderImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.Serial;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Bradley Moore on 10/17/2017.
 */
@Component
public class MultiTenantConnectionProviderImpl extends AbstractDataSourceBasedMultiTenantConnectionProviderImpl implements ApplicationListener<ContextRefreshedEvent> {

    @Serial
    private static final long serialVersionUID = 1L;
    
    private static final Logger log = LoggerFactory.getLogger(MultiTenantConnectionProviderImpl.class);

    @Value("${db.url:*****************************************************}")
    private String url;

    public static ConcurrentHashMap<String, DataSource> map; // thread-safe map holds the companyKey => DataSource

    private final CompanyRepository companyRepository;
    private final DataSource dataSource;
    private final TenantDatabaseConfig tenantDatabaseConfig;
    private final CurrentTenantResolverImpl currentTenantResolver;
    private final Environment environment;
    private final ConnectionPoolFactory connectionPoolFactory;

    @Autowired
    public MultiTenantConnectionProviderImpl(@Lazy CompanyRepository companyRepository,
                                             DataSource dataSource,
                                             TenantDatabaseConfig tenantDatabaseConfig,
                                             CurrentTenantResolverImpl currentTenantResolver,
                                             Environment environment,
                                             ConnectionPoolFactory connectionPoolFactory) {
        this.companyRepository = companyRepository;
        this.dataSource = dataSource;
        this.tenantDatabaseConfig = tenantDatabaseConfig;
        this.currentTenantResolver = currentTenantResolver;
        this.environment = environment;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @PostConstruct
    public void load() {
        map = new ConcurrentHashMap<>();
    }

    public void init() {
        load();
        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company company : companies) {
            try {
                addTenant(company);
            } catch (Exception e) {
                log.error("Error in database URL " + url.replace("nymbl_master", company.getKey()), e);
            }
        }
    }

    @Override
    protected DataSource selectAnyDataSource() {
        return map.getOrDefault(TenantContext.getCurrentTenant(), dataSource);
    }

    @Override
    protected DataSource selectDataSource(Object tenantIdentifier) {
        if (!map.containsKey((String) tenantIdentifier)) {
            // Possible new company added to DB since last restart.
            Company c = companyRepository.findByKey(tenantIdentifier.toString());
            if (c != null && c.getActive()) {
                addTenant(c);
            } else {
                log.error("Datasource is not found for " + tenantIdentifier + ".  Returning default value of nymbl_master.  This tenant is probably not set to active in the nymbl_master.user table");
            }
        }
        return map.getOrDefault(tenantIdentifier, dataSource);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // This is super critical to initialize after application is done with configuring beans.
        // otherwise you can not use companyRepository to fetch all the companies
        init();
    }

    public void addTenant(Company company) {
        org.apache.tomcat.jdbc.pool.DataSource ds = new org.apache.tomcat.jdbc.pool.DataSource();
        String companyUrl = url.replace("nymbl_master", company.getKey());
        boolean hasParams = companyUrl.contains("?");
        ds.setUrl(companyUrl + (hasParams ? "&" : "?") + "createDatabaseIfNotExist=true");
        ds.setUsername(company.getKey());
        ds.setPassword(company.getKey());
//        ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
        ds.setDriverClassName("software.aws.rds.jdbc.mysql.Driver");
        if (Arrays.asList(environment.getActiveProfiles()).contains("cron")) {
            ds.setMaxActive(50);
            ds.setInitialSize(10);
            ds.setMaxIdle(10);
            ds.setMinIdle(5);
            ds.setRemoveAbandonedTimeout(1200);
        } else {
            ds.setMaxActive(company.getDatabaseConnections());
            ds.setInitialSize(company.getDatabaseConnections() / 2);
            ds.setMaxIdle(company.getDatabaseConnections() / 2);
            ds.setMinIdle(3);
            ds.setRemoveAbandonedTimeout(600);
        }
        ds.setTimeBetweenEvictionRunsMillis(30000);
        ds.setMinEvictableIdleTimeMillis(60000);
        ds.setRemoveAbandoned(true);
        ds.setLogValidationErrors(true);
        ds.setTestOnConnect(true);
        ds.setTestWhileIdle(true);
        ds.setTestOnBorrow(true);
        ds.setValidationQuery("SELECT 1");
        tenantDatabaseConfig.tenantEntityManager(ds, this, currentTenantResolver);
        map.put(company.getKey(), ds);
    }
    
    /**
     * Get the tenant DataSources map for management operations.
     */
    public ConcurrentHashMap<String, DataSource> getTenantDataSources() {
        return map;
    }
    
    /**
     * Add a tenant DataSource.
     */
    public void addTenant(String tenantKey, DataSource dataSource) {
        if (tenantKey != null && dataSource != null) {
            map.put(tenantKey, dataSource);
            log.info("Added tenant DataSource: {}", tenantKey);
        }
    }
    
    /**
     * Remove a tenant DataSource.
     */
    public void removeTenant(String tenantKey) {
        if (tenantKey != null) {
            DataSource removed = map.remove(tenantKey);
            if (removed != null) {
                log.info("Removed tenant DataSource: {}", tenantKey);
            }
        }
    }
}
