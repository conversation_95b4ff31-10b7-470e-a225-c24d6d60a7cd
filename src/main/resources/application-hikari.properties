# HikariCP Configuration Profile
# Use this profile to enable HikariCP: --spring.profiles.active=hikari

# Enable HikariCP
nymbl.database.hikari.enabled=true
nymbl.database.pool.type=hikari

# HikariCP Performance Settings
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# HikariCP Monitoring
spring.datasource.hikari.register-mbeans=true

# Connection validation
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=5000

# Performance optimizations
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true
spring.datasource.hikari.data-source-properties.useLocalSessionState=true
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true
spring.datasource.hikari.data-source-properties.cacheResultSetMetadata=true
spring.datasource.hikari.data-source-properties.cacheServerConfiguration=true
spring.datasource.hikari.data-source-properties.elideSetAutoCommits=true
spring.datasource.hikari.data-source-properties.maintainTimeStats=false

# Logging
logging.level.com.zaxxer.hikari=DEBUG
logging.level.com.nymbl.database=DEBUG

# Metrics
management.endpoints.web.exposure.include=health,metrics,hikaricp
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true 