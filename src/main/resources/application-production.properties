# Production HikariCP Configuration
# Optimized for high-load production environments

# AWS Parameter Store settings for production
aws.paramstore.failFast=true
aws.paramstore.enabled=true

# HikariCP Settings (Conservative for production)
nymbl.database.hikari.enabled=false
nymbl.database.pool.type=tomcat

# Production-ready HikariCP settings (when enabled)
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=900000
spring.datasource.hikari.leak-detection-threshold=30000

# Monitoring enabled in production
spring.datasource.hikari.register-mbeans=true

# Production connection validation
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=3000

# Performance optimizations for production
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.prepStmtCacheSize=300
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true
spring.datasource.hikari.data-source-properties.useLocalSessionState=true
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true
spring.datasource.hikari.data-source-properties.cacheResultSetMetadata=true
spring.datasource.hikari.data-source-properties.cacheServerConfiguration=true
spring.datasource.hikari.data-source-properties.elideSetAutoCommits=true
spring.datasource.hikari.data-source-properties.maintainTimeStats=false

# Production logging (reduced verbosity)
logging.level.com.zaxxer.hikari=WARN
logging.level.com.nymbl.database=INFO
logging.level.com.nymbl.tenant=INFO

# Health checks and metrics
management.endpoints.web.exposure.include=health,metrics,hikaricp
management.endpoint.health.show-details=when-authorized
management.endpoint.metrics.enabled=true
management.endpoint.health.hikaricp.enabled=true

# Database monitoring frequency (production settings)
nymbl.database.monitoring.health-check-interval=300000
nymbl.database.monitoring.metrics-collection-interval=60000